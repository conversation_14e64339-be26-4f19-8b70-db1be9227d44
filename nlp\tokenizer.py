# tokenizer.py

SPECIAL_TOKENS = {
    "<PAD>": 0,
    "<UNK>": 1,
    "<START>": 2,   # a.k.a. <SOS>
    "<END>": 3      # a.k.a. <EOS>
}

def clean_text(text):
    punctuations = '.,!?;:()[]{}"\''
    cleaned = ''
    for ch in text.lower():
        cleaned += ch if ch not in punctuations else ' '
    return cleaned

def tokenize(text):
    return text.strip().split()

def count_words(tokenized_sentences):
    word_counts = {}
    for sentence in tokenized_sentences:
        for word in sentence:
            word_counts[word] = word_counts.get(word, 0) + 1
    return word_counts

def build_vocab(tokenized_sentences, max_vocab_size=None, min_freq=1):
    vocab = dict(SPECIAL_TOKENS)  # Start with special tokens
    reverse_vocab = {v: k for k, v in vocab.items()}
    idx = len(SPECIAL_TOKENS)

    word_counts = count_words(tokenized_sentences)

    # Filter by frequency
    filtered = [(word, count) for word, count in word_counts.items() if count >= min_freq]
    filtered.sort(key=lambda x: -x[1])  # sort descending by frequency

    if max_vocab_size:
        filtered = filtered[:max_vocab_size - len(SPECIAL_TOKENS)]

    for word, _ in filtered:
        if word not in vocab:
            vocab[word] = idx
            reverse_vocab[idx] = word
            idx += 1

    return vocab, reverse_vocab

def encode(tokenized_sentences, vocab):
    data = []
    unk_token = vocab["<UNK>"]
    start_token = vocab["<START>"]
    end_token = vocab["<END>"]

    for sentence in tokenized_sentences:
        indexed = [start_token]
        indexed.extend([vocab.get(word, unk_token) for word in sentence])
        indexed.append(end_token)
        data.append(indexed)

    return data

def decode(indexed_sentence, reverse_vocab):
    return ' '.join([reverse_vocab.get(idx, "<UNK>") for idx in indexed_sentence])

def pad_sequences(sequences, pad_token_id, max_len=None):
    if not max_len:
        max_len = max(len(seq) for seq in sequences)
    return [seq + [pad_token_id] * (max_len - len(seq)) for seq in sequences]

def save_vocab(vocab, path="vocab.txt"):
    with open(path, "w", encoding="utf-8") as f:
        for word, idx in vocab.items():
            f.write(f"{word}\t{idx}\n")

def load_vocab(path="vocab.txt"):
    vocab = {}
    reverse_vocab = {}
    with open(path, "r", encoding="utf-8") as f:
        for line in f:
            word, idx = line.strip().split('\t')
            idx = int(idx)
            vocab[word] = idx
            reverse_vocab[idx] = word
    return vocab, reverse_vocab

def process_file(path, max_vocab_size=None, min_freq=1):
    with open(path, "r", encoding="utf-8") as f:
        lines = f.readlines()

    tokenized_sentences = []
    for line in lines:
        cleaned = clean_text(line)
        tokens = tokenize(cleaned)
        if tokens:
            tokenized_sentences.append(tokens)

    vocab, reverse_vocab = build_vocab(tokenized_sentences, max_vocab_size=max_vocab_size, min_freq=min_freq)
    indexed_data = encode(tokenized_sentences, vocab)

    return tokenized_sentences, vocab, reverse_vocab, indexed_data

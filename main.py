'''
from nlp.tokenizer import process_file
from nlp.embedding import Embedding

# Step 1: Tokenize and get indexed data
tokens, vocab, rev_vocab, indexed_data = process_file("data/raw_text.txt")

# Step 2: Create embedding instance
embedding_dim = 4  # Example: 4-dimensional vectors
embedder = Embedding(vocab_size=len(vocab), embedding_dim=embedding_dim)

# Step 3: Embed one sentence
sentence_ids = indexed_data[0]  # e.g., [0, 1, 2]
embedded = embedder.embed_sentence(sentence_ids)

# Output
print("Original IDs:", sentence_ids)
print("Embedded vectors:")
for vec in embedded:
    print(vec)

from nlp.positional_encoding import encode_sentence_positions

length = 5  # Length of sentence
embedding_dim = 4  # Must match embedding dim
positional_vectors = encode_sentence_positions(length, embedding_dim)

print("Positional Encoding Vectors:")
for vec in positional_vectors:
    print(vec)
from transformer.attention import attention

# Fake inputs (3 tokens, 4-dim embeddings)
query = [[1, 0, 1, 0], [0, 1, 0, 1], [1, 1, 1, 1]]
key   = [[1, 0, 1, 0], [0, 1, 0, 1], [1, 1, 1, 1]]
value = [[0.1, 0.2, 0.3, 0.4], [0.5, 0.6, 0.7, 0.8], [0.9, 1.0, 1.1, 1.2]]

output, weights = attention(query, key, value)

print("Attention Output:")
for vec in output:
    print(vec)

print("\nAttention Weights:")
for row in weights:
    print(row)
from transformer.feedforward import FeedForward

# One input token (4-dim embedding)
input_vec = [1.0, 0.5, 0.2, 0.1]

# Create feedforward with d_model=4, d_ff=8
ffn = FeedForward(d_model=4, d_ff=8)

# Pass through FFN
output_vec = ffn.forward(input_vec)

print("Output Vector:", output_vec)
'''
#----------------------------------------------------------------------------------

# # --- Save vocab to file ---
# import random
# from nlp.tokenizer import process_file
# from nlp.embedding import Embedding
# from nlp.positional_encoding import encode_sentence_positions
# from transformer.encoder import Encoder
# #from transformer.decoder import TransformerDecoder
# from classifier.classifier import Classifier
# from utils.math_ops import softmax

# # --- Save vocab to file ---
# def save_vocab(vocab, filepath="data/vocab.txt"):
#     with open(filepath, "w", encoding="utf-8") as f:
#         for word, idx in vocab.items():
#             f.write(f"{word} {idx}\n")

# # --- Save indexed sentences to file ---
# def save_indexed_data(indexed_data, filepath="data/preprocessed_data.txt"):
#     with open(filepath, "w", encoding="utf-8") as f:
#         for sentence in indexed_data:
#             f.write(" ".join(map(str, sentence)) + "\n")

# def main():
#     random.seed(42)

#     # Step 1: Tokenization
#     tokens, vocab, rev_vocab, indexed_data = process_file("data/raw_text.txt")
#     save_vocab(vocab, "data/vocab.txt")
#     save_indexed_data(indexed_data, "data/preprocessed_data.txt")

#     print("\n=== Tokenization Done ===")
#     print("Sample Tokens:", tokens[:5])

#     # Step 2: Embedding
#     embedding_dim = 128
#     embedder = Embedding(vocab_size=len(vocab), embedding_dim=embedding_dim)
#     sentence_ids = indexed_data[0] if indexed_data else []
#     embedded = embedder.embed_sentence(sentence_ids)

#     # Step 3: Positional Encoding
#     pos_encoded = encode_sentence_positions(len(sentence_ids), embedding_dim)

#     # Combine embeddings and position encodings
#     encoder_input = [[e + p for e, p in zip(embed, pos)] for embed, pos in zip(embedded, pos_encoded)]

#     # Step 4: Encoder Forward Pass
#     encoder = Encoder(num_layers=2, d_model=embedding_dim, d_ff=embedding_dim * 4, residual_scale=1.0)
#     encoder_output = encoder.forward(encoder_input)

#     print("\n=== Encoder Output Ready ===")


#     # Step 6: Classification
#     num_classes = 2
#     classifier = Classifier(input_dim=embedding_dim, num_classes=num_classes)
#     logits = classifier.forward(encoder_output)

#     print("\n=== Classifier Output (Logits) ===")
#     print(logits)
#     print("Logit Difference:", abs(logits[0] - logits[1]))

#     # Step 7: Softmax with Temperature Scaling
#     temperature = 3.0
#     scaled_logits = [logit / temperature for logit in logits]
#     probs = softmax(scaled_logits)

#     print(f"\n=== Probabilities (T={temperature}) ===")
#     print(probs)

#     # Encoder Output Stats
#     flat = [val for vec in encoder_output for val in vec]
#     print(f"\n=== Encoder Output Stats ===")
#     print(f"Min: {min(flat):.4f}, Max: {max(flat):.4f}, Mean: {sum(flat)/len(flat):.4f}")

# if __name__ == "__main__":
#     main()


#-----------------------------------------------------------------------------------------------
# import random
# from nlp.tokenizer import process_file, decode
# from nlp.embedding import Embedding
# from nlp.positional_encoding import encode_sentence_positions
# from transformer.encoder import Encoder
# #from transformer.decoder import TransformerDecoder
# from classifier.classifier import Classifier
# from utils.math_ops import softmax

# def save_vocab(vocab, filepath="data/vocab.txt"):
#     with open(filepath, "w", encoding="utf-8") as f:
#         for word, idx in vocab.items():
#             f.write(f"{word} {idx}\n")

# def save_indexed_data(indexed_data, filepath="data/preprocessed_data.txt"):
#     with open(filepath, "w", encoding="utf-8") as f:
#         for sentence in indexed_data:
#             f.write(" ".join(map(str, sentence)) + "\n")

# def main():
#     random.seed(42)

#     # Step 1: Tokenization
#     tokens, vocab, rev_vocab, indexed_data = process_file("data/raw_text.txt")
#     save_vocab(vocab, "data/vocab.txt")
#     save_indexed_data(indexed_data, "data/preprocessed_data.txt")

#     print("\n=== Tokenization Done ===")
#     print("Sample Tokens:", tokens[:5])

#     # Step 2: Embedding
#     embedding_dim = 128
#     embedder = Embedding(vocab_size=len(vocab), embedding_dim=embedding_dim)
#     sentence_ids = indexed_data[0] if indexed_data else []
#     embedded = embedder.embed_sentence(sentence_ids)

#     # Step 3: Positional Encoding
#     pos_encoded = encode_sentence_positions(len(sentence_ids), embedding_dim)

#     # Combine embeddings and position encodings
#     encoder_input = [[e + p for e, p in zip(embed, pos)] for embed, pos in zip(embedded, pos_encoded)]

#     # Step 4: Encoder Forward Pass
#     encoder = Encoder(num_layers=2, d_model=embedding_dim, d_ff=embedding_dim * 4, residual_scale=1.0)
#     encoder_output = encoder.forward(encoder_input)

#     print("\n=== Encoder Output Ready ===")

#     # # Step 5: Decoder Generation
#     # start_token_id = vocab.get("<START>", 1)
#     # end_token_id = vocab.get("<END>", 2)

#     # decoder = TransformerDecoder(
#     #     num_layers=2,
#     #     d_model=embedding_dim,
#     #     d_ff=embedding_dim * 4,
#     #     residual_scale=1.0
#     # )

#     # generated_token_ids = decoder.generate(
#     #     encoder_output=encoder_output,
#     #     start_token=start_token_id,
#     #     tokenizer=embedder,
#     #     max_len=20,
#     #     method="greedy"
#     # )

#     # decoded_text = decode(generated_token_ids, rev_vocab)

#     # print("\n=== Decoder Output ===")
#     # print("Generated Token IDs:", generated_token_ids)
#     # print("Generated Text:", decoded_text)

#     # Step 6: Classification
#     num_classes = 2
#     classifier = Classifier(input_dim=embedding_dim, num_classes=num_classes)
#     logits = classifier.forward(encoder_output)

#     print("\n=== Classifier Output (Logits) ===")
#     print(logits)
#     print("Logit Difference:", abs(logits[0] - logits[1]))

#     # Step 7: Softmax with Temperature Scaling
#     temperature = 3.0
#     scaled_logits = [logit / temperature for logit in logits]
#     probs = softmax(scaled_logits)

#     print(f"\n=== Probabilities (T={temperature}) ===")
#     print(probs)

#     # Encoder Output Stats
#     flat = [val for vec in encoder_output for val in vec]
#     print(f"\n=== Encoder Output Stats ===")
#     print(f"Min: {min(flat):.4f}, Max: {max(flat):.4f}, Mean: {sum(flat)/len(flat):.4f}")

# if __name__ == "__main__":
#     main()

#---------------------------------------------------------------------------------
# # main.py
# import random
# from nlp.tokenizer import process_file
# from nlp.embedding import Embedding
# from nlp.positional_encoding import encode_sentence_positions
# from transformer.encoder import Encoder
# from classifier.classifier import Classifier
# from utils.math_ops import softmax
# from gan.generator import Generator
# from transformer.decoder import Decoder

# def save_vocab(vocab, filepath="data/vocab.txt"):
#     with open(filepath, "w", encoding="utf-8") as f:
#         for word, idx in vocab.items():
#             f.write(f"{word} {idx}\n")

# def save_indexed_data(indexed_data, filepath="data/preprocessed_data.txt"):
#     with open(filepath, "w", encoding="utf-8") as f:
#         for sentence in indexed_data:
#             f.write(" ".join(map(str, sentence)) + "\n")

# def main():
#     random.seed(42)

#     # Step 1: Tokenize
#     tokens, vocab, rev_vocab, indexed_data = process_file("data/raw_text.txt")
#     save_vocab(vocab)
#     save_indexed_data(indexed_data)

#     print("\n✅ Tokenization Done")
#     print("Sample Tokens:", tokens[:5])

#     if not indexed_data:
#         print("No data found!")
#         return

#     # Step 2: Embedding
#     embedding_dim = 128
#     embedder = Embedding(vocab_size=len(vocab), embedding_dim=embedding_dim)
#     sentence_ids = indexed_data[0]  # use first sentence for testing
#     embedded = embedder.embed_sentence(sentence_ids)

#     # Step 3: Positional Encoding
#     pos_encoded = encode_sentence_positions(len(sentence_ids), embedding_dim)

#     # Step 4: Combine embedding + position
#     encoder_input = [
#         [e + p for e, p in zip(embed, pos)]
#         for embed, pos in zip(embedded, pos_encoded)
#     ]

#     # Step 5: Encoder
#     encoder = Encoder(num_layers=2, d_model=embedding_dim, d_ff=embedding_dim * 4, residual_scale=1.0)
#     encoder_output = encoder.forward(encoder_input)
#     print("\n✅ Encoder Output Ready")

#     # Step 6: Classifier
#     num_classes = 2
#     classifier = Classifier(input_dim=embedding_dim, num_classes=num_classes)
#     logits = classifier.forward(encoder_output)

#     print("\n✅ Classifier Logits:", logits)
#     print("Logit Difference:", abs(logits[0] - logits[1]))

#     # Step 7: Softmax with temperature scaling
#     temperature = 3.0
#     scaled_logits = [logit / temperature for logit in logits]
#     probs = softmax(scaled_logits)

#     print(f"\n✅ Probabilities (Temp={temperature}):", probs)

#     # Step 8: Stats
#     flat = [val for vec in encoder_output for val in vec]
#     print(f"\n✅ Encoder Output Stats — Min: {min(flat):.4f}, Max: {max(flat):.4f}, Mean: {sum(flat)/len(flat):.4f}")

#     # Step 9: Test Generator (GAN)
#     generator = Generator(vocab_size=len(vocab), embedding_dim=embedding_dim)
#     gen_sentence = generator.generate(prompt_tokens=[2], max_gen_len=10)  # <START> token ID = 2
#     print("\n✅ Generator Output (Token IDs):", gen_sentence)
#     print("Generated Sentence:", " ".join(rev_vocab.get(tok, "<UNK>") for tok in gen_sentence))

#     # Step 10: Decoder (manual test - step-wise decoding using forward_step)
#     decoder = Decoder(num_layers=2, d_model=embedding_dim, d_ff=32)  # Adjust layer params if needed

#     decoder_input_ids = [2]  # <START> token
#     generated_ids = decoder_input_ids[:]

#     for _ in range(10):
#         # Embed tokens
#         embeddings = embedder.embed_sentence(generated_ids)

#         # Add positional encodings
#         pos_encoded = encode_sentence_positions(
#             sentence_length=len(embeddings),
#             d_model=embedding_dim,
#             scale=True,
#             normalize=False
#         )
#         decoder_input = [
#             [e + p for e, p in zip(embed, pos)]
#             for embed, pos in zip(embeddings, pos_encoded)
#         ]

#         # Get decoder output logits
#         output_logits = decoder.forward_step(decoder_input)

#         # Use logits from last token
#         next_logits = output_logits[-1]
#         next_token = generator.sample_from_logits(next_logits)

#         generated_ids.append(next_token)
#         if next_token == 3:  # <END>
#             break

#     print("\n✅ Decoder Output (Token IDs):", generated_ids)
#     print("Decoded Sentence:", " ".join(rev_vocab.get(tok, "<UNK>") for tok in generated_ids))


#-------------------------------------------------------------------------



# main.py

# import os
# import random
# import pickle

# from nlp.tokenizer import process_file
# from nlp.embedding import Embedding
# from nlp.positional_encoding import encode_sentence_positions
# from transformer.encoder import Encoder
# from classifier.classifier import Classifier
# from utils.math_ops import softmax
# from gan.generator import Generator
# from gan.trainer import Trainer
# from transformer.decoder import Decoder


# def save_vocab(vocab, filepath="data/vocab.txt"):
#     with open(filepath, "w", encoding="utf-8") as f:
#         for word, idx in vocab.items():
#             f.write(f"{word} {idx}\n")


# def save_indexed_data(indexed_data, filepath="data/preprocessed_data.txt"):
#     with open(filepath, "w", encoding="utf-8") as f:
#         for sentence in indexed_data:
#             f.write(" ".join(map(str, sentence)) + "\n")


# def save_generator(generator, path="models/trained_generator.pkl"):
#     os.makedirs(os.path.dirname(path), exist_ok=True)
#     with open(path, "wb") as f:
#         pickle.dump(generator, f)
#     print(f"✅ Generator saved to: {path}")


# def load_generator(path="models/trained_generator.pkl"):
#     if not os.path.exists(path):
#         raise FileNotFoundError(f"❌ No trained generator found at {path}")
#     with open(path, "rb") as f:
#         generator = pickle.load(f)
#     print(f"✅ Loaded trained generator from: {path}")
#     return generator


# def main(train_gan=True, generate_only=False):
#     random.seed(42)

#     # Step 1: Tokenize
#     tokens, vocab, rev_vocab, indexed_data = process_file("data/raw_text.txt")
#     save_vocab(vocab)
#     save_indexed_data(indexed_data)
#     print("\n✅ Tokenization Done")
#     print("Sample Tokens:", tokens[:5])

#     if not indexed_data:
#         print("No data found!")
#         return

#     embedding_dim = 128

#     if train_gan:
#         # Step 2: Train GAN
#         print("\n🚀 Training GAN (Generator + Discriminator)...")
#         trainer = Trainer(
#             vocab=vocab,
#             rev_vocab=rev_vocab,
#             max_len=10,
#             embedding_dim=embedding_dim,
#             hidden_dim=256,
#             num_heads=4
#         )
#         trainer.train(indexed_data, epochs=20)

#         # Save the trained generator
#         save_generator(trainer.generator)

#     # Step 3: Use Trained Generator to Generate Sentence
#     generator = load_generator()

#     start_token = vocab.get("<START>", 2)
#     gen_sentence = generator.generate(prompt_tokens=[start_token], max_gen_len=10)
#     print("\n✅ Generated Sentence (Token IDs):", gen_sentence)
#     print("📝 Decoded:", " ".join(rev_vocab.get(tok, "<UNK>") for tok in gen_sentence))

#     # (Optional) Decoder test
#     decoder = Decoder(num_layers=2, d_model=embedding_dim, d_ff=256)
#     decoder_input_ids = [start_token]
#     embedder = Embedding(vocab_size=len(vocab), embedding_dim=embedding_dim)
#     generated_ids = decoder_input_ids[:]

#     for _ in range(10):
#         embeddings = embedder.embed_sentence(generated_ids)
#         pos_encoded = encode_sentence_positions(len(embeddings), embedding_dim)
#         decoder_input = [
#             [e + p for e, p in zip(embed, pos)]
#             for embed, pos in zip(embeddings, pos_encoded)
#         ]
#         output_logits = decoder.forward_step(decoder_input)
#         next_logits = output_logits[-1]
#         next_token = generator.sample_from_logits(next_logits)
#         generated_ids.append(next_token)
#         if next_token == vocab.get("<END>", 3):
#             break

#     print("\n✅ Decoder Output (Token IDs):", generated_ids)
#     print("📝 Decoded:", " ".join(rev_vocab.get(tok, "<UNK>") for tok in generated_ids))


# if __name__ == "__main__":
#     main(train_gan=True)  # Set to False if you only want to load and generate


# # if __name__ == "__main__":
# #     main()




#------------------------------------------------------------
import os
import random
import pickle

from nlp.tokenizer import process_file
from nlp.embedding import Embedding
from nlp.positional_encoding import encode_sentence_positions
from transformer.encoder import Encoder
from classifier.classifier import Classifier
from utils.math_ops import softmax
from gan.generator import Generator
from gan.discriminator import Discriminator
from gan.trainer import Trainer
from transformer.decoder import Decoder


def save_vocab(vocab, filepath="data/vocab.txt"):
    os.makedirs(os.path.dirname(filepath), exist_ok=True)
    with open(filepath, "w", encoding="utf-8") as f:
        for word, idx in vocab.items():
            f.write(f"{word} {idx}\n")


def save_indexed_data(indexed_data, filepath="data/preprocessed_data.txt"):
    os.makedirs(os.path.dirname(filepath), exist_ok=True)
    with open(filepath, "w", encoding="utf-8") as f:
        for sentence in indexed_data:
            f.write(" ".join(map(str, sentence)) + "\n")


def save_generator(generator, path="models/trained_generator.pkl"):
    os.makedirs(os.path.dirname(path), exist_ok=True)
    with open(path, "wb") as f:
        pickle.dump(generator, f)
    print(f"✅ Generator saved to: {path}")


def load_generator(path="models/trained_generator.pkl"):
    if not os.path.exists(path):
        raise FileNotFoundError(f"❌ No trained generator found at {path}")
    with open(path, "rb") as f:
        generator = pickle.load(f)
    print(f"✅ Loaded trained generator from: {path}")
    return generator


def run_encoder_classifier(indexed_data, embedding_dim, vocab):
    sentence_ids = indexed_data[0]
    embedder = Embedding(vocab_size=len(vocab), embedding_dim=embedding_dim)
    embedded = embedder.embed_sentence(sentence_ids)
    pos_encoded = encode_sentence_positions(len(sentence_ids), embedding_dim)
    encoder_input = [[e + p for e, p in zip(embed, pos)] for embed, pos in zip(embedded, pos_encoded)]

    encoder = Encoder(num_layers=2, d_model=embedding_dim, d_ff=embedding_dim * 4, residual_scale=1.0)
    encoder_output = encoder.forward(encoder_input)
    print("\n✅ Encoder Output Ready")

    classifier = Classifier(input_dim=embedding_dim, num_classes=2)
    logits = classifier.forward(encoder_output)
    print("✅ Classifier Logits:", logits)

    temperature = 3.0
    scaled_logits = [logit / temperature for logit in logits]
    probs = softmax(scaled_logits)
    print(f"✅ Probabilities (Temp={temperature}):", probs)

    flat = [val for vec in encoder_output for val in vec]
    print(f"✅ Encoder Output Stats — Min: {min(flat):.4f}, Max: {max(flat):.4f}, Mean: {sum(flat)/len(flat):.4f}")


def run_decoder_test(generator, vocab, rev_vocab, embedding_dim):
    decoder = Decoder(num_layers=2, d_model=embedding_dim, d_ff=256, vocab_size=len(vocab))
    embedder = Embedding(vocab_size=len(vocab), embedding_dim=embedding_dim)
    start_token = vocab.get("<START>", 2)
    end_token = vocab.get("<END>", 3)
    decoder_input_ids = [start_token]
    generated_ids = decoder_input_ids[:]

    for _ in range(10):
        embeddings = embedder.embed_sentence(generated_ids)
        pos_encoded = encode_sentence_positions(len(embeddings), embedding_dim)
        decoder_input = [[e + p for e, p in zip(embed, pos)] for embed, pos in zip(embeddings, pos_encoded)]
        output_logits = decoder.forward_step(decoder_input)
        next_logits = output_logits
        next_token = generator.sample_from_logits(next_logits, temperature=0.6)
        generated_ids.append(next_token)
        if next_token == end_token:
            break

    print("\n✅ Decoder Output (Token IDs):", generated_ids)
    print("📝 Decoded:", " ".join(rev_vocab.get(tok, "<UNK>") for tok in generated_ids))


def main(train_gan=True, run_encoder_cls=True):
    random.seed(42)

    # Final optimization: Balanced dataset for quality vs speed
    tokens, vocab, rev_vocab, indexed_data = process_file("data/raw_text.txt", max_vocab_size=1500, min_freq=2)
    if len(indexed_data) > 100:
        indexed_data = indexed_data[:100]
        print(f"🎯 Final optimization: Using first 100 sentences for quality training (out of {len(tokens)} total)")
    else:
        print(f"🎯 Using all {len(indexed_data)} sentences for training")
    save_vocab(vocab)
    save_indexed_data(indexed_data)
    print("\n✅ Tokenization Done")
    print("Sample Tokens:", tokens[:5])
    print(f"📊 Vocabulary size: {len(vocab)} words")
    print(f"📊 Training sentences: {len(indexed_data)}")
    print(f"📊 Total sentences available: {len(tokens)}")

    if not indexed_data:
        print("❌ No data found!")
        return

    embedding_dim = 128

    if run_encoder_cls:
        run_encoder_classifier(indexed_data, embedding_dim, vocab)

    if train_gan:
        print("\n🚀 Training GAN (Generator + Discriminator)...")
        generator = Generator(
            vocab_size=len(vocab),
            embedding_dim=embedding_dim,
            hidden_dim=256,
            max_len=10,
            num_heads=4
        )

        discriminator = Discriminator(
            vocab_size=len(vocab),
            embedding_dim=embedding_dim,
            hidden_dim=256,
            num_heads=4,
            max_len=10
        )

        trainer = Trainer(
            generator=generator,
            discriminator=discriminator,
            vocab=vocab,
            rev_vocab=rev_vocab,
            max_len=10,
            embedding_dim=embedding_dim,
            hidden_dim=256,
            num_heads=4
        )
        trainer.train(indexed_data, epochs=5)
        save_generator(generator)

    generator = load_generator()

    start_token = vocab.get("<START>", 2)
    # Final optimization: Stronger repetition penalty and better parameters
    gen_sentence = generator.generate(
        input_tokens=[start_token],
        max_len=10,
        temperature=0.6,  # Even more focused
        mode="nucleus",
        top_p=0.85,  # Slightly more selective
        repetition_penalty=1.8  # Much stronger anti-repetition
    )
    print("\n✅ Generated Sentence (Token IDs):", gen_sentence)
    print("📝 Decoded:", " ".join(rev_vocab.get(tok, "<UNK>") for tok in gen_sentence))

    run_decoder_test(generator, vocab, rev_vocab, embedding_dim)


if __name__ == "__main__":
    main(
        train_gan=True,         # Train GAN
        run_encoder_cls=True    # If False, skip encoder/classifier step
    )


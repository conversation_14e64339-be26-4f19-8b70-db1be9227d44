# gan/discriminator.py

import random
from nlp.embedding import Embedding
from nlp.positional_encoding import encode_sentence_positions
from transformer.self_attention import SelfAttention
from transformer.feedforward import FeedForward
from utils.math_ops import add_vectors
from classifier.classifier import Classifier


class Discriminator:
    def __init__(self, vocab_size, embedding_dim, hidden_dim, num_heads, max_len=10):
        self.vocab_size = vocab_size
        self.embedding_dim = embedding_dim
        self.hidden_dim = hidden_dim
        self.num_heads = num_heads
        self.max_len = max_len

        self.embedding = Embedding(vocab_size=vocab_size, embedding_dim=embedding_dim)
        self.self_attn = SelfAttention(d_model=embedding_dim, causal=False, num_heads=num_heads)
        self.ffn = FeedForward(d_model=embedding_dim, d_ff=hidden_dim)
        self.classifier = Classifier(input_dim=embedding_dim, num_classes=2)

    def forward(self, token_ids):
        """
        token_ids: List[int] — token ids for a single sequence
        """
        if not token_ids:
            raise ValueError("Empty token_ids provided to Discriminator.")

        # Embed and add positional encodings
        embedded = self.embedding.embed_sentence(token_ids)  # [seq_len, d_model]
        pos_encoded = encode_sentence_positions(len(token_ids), self.embedding_dim)
        input_seq = [add_vectors(e, p) for e, p in zip(embedded, pos_encoded)]

        # Apply self-attention
        attn_output = self.self_attn.forward(input_seq)  # [seq_len, d_model]

        # Apply feedforward network
        ffn_output = self.ffn.forward(attn_output)  # [seq_len, d_model]

        # Use average pooling across the sequence to get a fixed-length representation
        avg_vector = [sum(x[i] for x in ffn_output) / len(ffn_output) for i in range(self.embedding_dim)]

        # Feed to classifier
        logits = self.classifier.forward([avg_vector])  # Input is 1 sample

        return logits  # [2-class scores]

    def discriminate(self, token_ids):
        """
        Returns a probability score that the sequence is real (vs generated).
        """
        logits = self.forward(token_ids)
        score = self._sigmoid(logits[0][1])  # Probability of being 'real'
        return score

    def _sigmoid(self, x):
        return 1 / (1 + pow(2.71828, -x))

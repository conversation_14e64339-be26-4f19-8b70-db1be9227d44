# transformer/decoder.py

import math
import random
from transformer.self_attention import SelfAtten<PERSON>
from transformer.feedforward import FeedForward
from utils.math_ops import add_vectors, scale_vector


class DecoderLayer:
    def __init__(self, d_model, d_ff, residual_scale=1.0):
        self.self_attn = SelfAttention(d_model, causal=True)
        self.enc_dec_attn = SelfAttention(d_model)
        self.ff = FeedForward(d_model, d_ff)
        self.residual_scale = residual_scale

    def layer_norm(self, x):
        mean = sum(x) / len(x)
        variance = sum((xi - mean) ** 2 for xi in x) / len(x)
        std_dev = math.sqrt(variance + 1e-8)
        return [(xi - mean) / std_dev for xi in x]

    def forward_step(self, x_sequence, encoder_output):
        norm_seq = [self.layer_norm(vec) for vec in x_sequence]
        self_attn_out = self.self_attn.forward(norm_seq)
        x = [add_vectors(orig, scale_vector(attn, self.residual_scale)) for orig, attn in zip(x_sequence, self_attn_out)]

        # Only apply encoder-decoder attention if encoder_output is provided
        if encoder_output is not None:
            norm_seq2 = [self.layer_norm(vec) for vec in x]
            enc_dec_attn_out = self.enc_dec_attn.forward(norm_seq2, context=encoder_output)
            x = [add_vectors(orig, scale_vector(attn, self.residual_scale)) for orig, attn in zip(x, enc_dec_attn_out)]

        norm_seq3 = [self.layer_norm(vec) for vec in x]
        ff_out = self.ff.forward_batch(norm_seq3)
        x = [add_vectors(orig, scale_vector(ff, self.residual_scale)) for orig, ff in zip(x, ff_out)]

        return x


class Decoder:
    def __init__(self, num_layers, d_model, d_ff, vocab_size, residual_scale=1.0):
        self.layers = [
            DecoderLayer(d_model, d_ff, residual_scale)
            for _ in range(num_layers)
        ]
        self.d_model = d_model
        self.vocab_size = vocab_size

        # Output projection layer: projects decoder output to vocab logits
        self.output_projection = [
            [random.uniform(-0.1, 0.1) for _ in range(d_model)]
            for _ in range(vocab_size)
        ]
        self.output_bias = [0.0 for _ in range(vocab_size)]

    def project_to_vocab(self, hidden_vector):
        """Project the last decoder hidden vector to vocabulary logits"""
        return [
            sum(w * h for w, h in zip(weight_row, hidden_vector)) + bias
            for weight_row, bias in zip(self.output_projection, self.output_bias)
        ]

    def forward_step(self, inputs, encoder_output=None):
        """
        inputs: list of input token vectors
        Returns: logits over vocabulary for next token
        """
        x_sequence = inputs
        for layer in self.layers:
            x_sequence = layer.forward_step(x_sequence, encoder_output)

        final_hidden = x_sequence[-1]  # last token representation
        vocab_logits = self.project_to_vocab(final_hidden)
        return vocab_logits

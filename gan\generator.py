# gan/generator.py

import math
import random
from nlp.embedding import Embedding
from nlp.positional_encoding import encode_sentence_positions
from transformer.decoder import Decoder<PERSON>ayer
from utils.math_ops import softmax, matmul, transpose, initialize_matrix


class Generator:
    def __init__(self, vocab_size, embedding_dim, hidden_dim, max_len, num_heads):
        self.vocab_size = vocab_size
        self.embedding_dim = embedding_dim
        self.max_len = max_len

        self.embedding = Embedding(vocab_size, embedding_dim)
        self.decoder = DecoderLayer(embedding_dim, hidden_dim, residual_scale=1.0)

        # Output projection matrix: maps decoder output to vocab logits
        self.output_weights = initialize_matrix(vocab_size, embedding_dim)
        self.output_bias = [0.0] * vocab_size

    def project_to_vocab(self, decoder_output):
        """
        Project the final decoder hidden state to vocab logits using matrix multiplication.
        decoder_output: List[Float] (embedding_dim)
        returns: List[Float] (vocab_size)
        """
        logits = []
        for i in range(self.vocab_size):
            logit = sum([decoder_output[j] * self.output_weights[i][j] for j in range(self.embedding_dim)])
            logit += self.output_bias[i]
            logits.append(logit)
        return logits

    def softmax_with_temperature(self, logits, temperature=1.0):
        scaled_logits = [x / temperature for x in logits]
        return softmax(scaled_logits)

    def sample_token(self, logits, temperature=1.0, mode="multinomial", top_p=0.9, repetition_penalty=1.2, generated_tokens=None):
        # Phase 5: Apply repetition penalty
        if generated_tokens and repetition_penalty != 1.0:
            for token in set(generated_tokens):
                if token < len(logits):
                    logits[token] /= repetition_penalty

        probs = self.softmax_with_temperature(logits, temperature)

        # Phase 5: Nucleus sampling (top-p)
        if mode == "nucleus" and top_p < 1.0:
            return self.nucleus_sample(probs, top_p)
        elif mode == "multinomial":
            return self.multinomial_sample(probs)
        else:
            return probs.index(max(probs))  # greedy

    def multinomial_sample(self, probs):
        total = sum(probs)
        r = random.uniform(0, total)
        upto = 0
        for i, p in enumerate(probs):
            if upto + p >= r:
                return i
            upto += p
        return len(probs) - 1  # fallback

    def nucleus_sample(self, probs, top_p):
        """Phase 5: Nucleus (top-p) sampling for better quality"""
        # Sort probabilities in descending order
        sorted_indices = sorted(range(len(probs)), key=lambda i: probs[i], reverse=True)
        cumulative_prob = 0
        nucleus_indices = []

        for idx in sorted_indices:
            cumulative_prob += probs[idx]
            nucleus_indices.append(idx)
            if cumulative_prob >= top_p:
                break

        # Renormalize probabilities within nucleus
        nucleus_probs = [probs[i] for i in nucleus_indices]
        total = sum(nucleus_probs)
        nucleus_probs = [p / total for p in nucleus_probs]

        # Sample from nucleus
        r = random.uniform(0, 1)
        upto = 0
        for i, p in enumerate(nucleus_probs):
            if upto + p >= r:
                return nucleus_indices[i]
            upto += p
        return nucleus_indices[-1]  # fallback

    def sample_from_logits(self, logits, temperature=1.0):
        """Sample a token from logits using temperature scaling"""
        return self.sample_token(logits, temperature, mode="multinomial")

    def generate(self, input_tokens, max_len, temperature=1.0, mode="nucleus", top_p=0.9, repetition_penalty=1.2):
        generated = input_tokens[:]
        for _ in range(max_len - len(input_tokens)):
            # Step 1: Embed tokens
            embedded = self.embedding.embed_sentence(generated)
            # Step 2: Add positional encoding
            positions = encode_sentence_positions(len(generated), self.embedding_dim)
            # Combine embeddings and positional encodings
            encoded = [[e + p for e, p in zip(embed, pos)] for embed, pos in zip(embedded, positions)]
            # Step 3: Pass through decoder (DecoderLayer expects forward_step with encoder_output)
            decoded = self.decoder.forward_step(encoded, encoder_output=None)
            # Step 4: Project last hidden state to vocab logits
            next_logits = self.project_to_vocab(decoded[-1])
            # Step 5: Sample next token with improved sampling
            next_token = self.sample_token(
                logits=next_logits,
                temperature=temperature,
                mode=mode,
                top_p=top_p,
                repetition_penalty=repetition_penalty,
                generated_tokens=generated
            )
            generated.append(next_token)
        return generated

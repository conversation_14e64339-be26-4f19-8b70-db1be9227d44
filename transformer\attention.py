# transformer/attention.py

import math
from utils.math_ops import dot_product, softmax, matmul, transpose, split_heads, combine_heads

def apply_causal_mask(scores):
    """
    Prevents attention to future tokens by applying a causal mask.
    Sets future positions to -inf in attention scores.
    Args:
        scores: List[List[float]] — [seq_len x seq_len]
    Returns:
        masked_scores: List[List[float]]
    """
    masked_scores = []
    for i, row in enumerate(scores):
        masked_row = [score if j <= i else float('-inf') for j, score in enumerate(row)]
        masked_scores.append(masked_row)
    return masked_scores

def apply_padding_mask(scores, pad_mask):
    """
    Applies a padding mask to attention scores.
    Args:
        scores: List[List[float]] — [seq_len x seq_len]
        pad_mask: List[bool] — True for real tokens, False for padding
    Returns:
        masked_scores: List[List[float]]
    """
    masked_scores = []
    for row in scores:
        if len(row) != len(pad_mask):
            raise ValueError("Score row and pad_mask must be the same length")
        masked_row = [score if pad_mask[j] else float('-inf') for j, score in enumerate(row)]
        masked_scores.append(masked_row)
    return masked_scores

def attention(query, key, value, mask_type=None, pad_mask=None):
    """
    Basic scaled dot-product attention mechanism.
    Args:
        query, key, value: List[List[float]]
        mask_type: "causal" or None
        pad_mask: Optional List[bool]
    Returns:
        output: List[List[float]]
        attention_weights: List[List[float]]
    """
    if not (len(query) == len(value) and len(key) == len(value)):
        raise ValueError("query, key, and value must have the same sequence length")

    d_k = len(query[0])  # Embedding dimension
    key_T = transpose(key)  # Transpose for dot product

    # Step 1: Raw attention scores
    scores = [
        [dot_product(q, k_col) for k_col in key_T]
        for q in query
    ]

    # Step 2: Scale scores
    scale = math.sqrt(d_k)
    scaled_scores = [[s / scale for s in row] for row in scores]

    # Step 3: Masking
    if mask_type == "causal":
        scaled_scores = apply_causal_mask(scaled_scores)
    if pad_mask is not None:
        scaled_scores = apply_padding_mask(scaled_scores, pad_mask)

    # Step 4: Softmax to get attention weights
    attention_weights = [softmax(row) for row in scaled_scores]

    # Step 5: Weighted sum of values
    output = matmul(attention_weights, value)

    return output, attention_weights

def multi_head_attention(query, key, value, num_heads=2, mask_type=None, pad_mask=None):
    """
    Multi-head attention wrapper.
    Args:
        query, key, value: List[List[float]]
        num_heads: int
        mask_type: Optional str
        pad_mask: Optional List[bool]
    Returns:
        combined_output: List[List[float]]
        all_weights: List of List[List[float]] (per head)
    """
    d_model = len(query[0])
    assert d_model % num_heads == 0, "d_model must be divisible by num_heads"

    # Step 1: Split Q, K, V into heads
    q_heads = split_heads(query, num_heads)
    k_heads = split_heads(key, num_heads)
    v_heads = split_heads(value, num_heads)

    # Step 2: Apply attention to each head
    head_outputs = []
    all_weights = []
    for h in range(num_heads):
        out, weights = attention(q_heads[h], k_heads[h], v_heads[h], mask_type, pad_mask)
        head_outputs.append(out)
        all_weights.append(weights)

    # Step 3: Concatenate heads
    combined_output = combine_heads(head_outputs)

    return combined_output, all_weights

# transformer/encoder.py

from transformer.feedforward import FeedForward
from transformer.self_attention import SelfAttention
from utils.math_ops import add_vectors, scale_vector, layer_norm

class EncoderLayer:
    def __init__(self, d_model, d_ff, residual_scale=1.0, use_checkpointing=False):
        self.self_attn = SelfAttention(d_model)
        self.ff = FeedForward(d_model, d_ff)
        self.residual_scale = residual_scale
        self.use_checkpointing = use_checkpointing

    def forward(self, x):
        # --- Pre-Norm Self-Attention ---
        norm_x = [layer_norm(vec) for vec in x]
        attn_out = self.self_attn.forward(norm_x)

        # Residual connection with scaling
        x = [add_vectors(orig, scale_vector(attn, self.residual_scale)) for orig, attn in zip(x, attn_out)]

        # --- Pre-Norm FeedForward ---
        norm_x2 = [layer_norm(vec) for vec in x]
        ff_out = self.ff.forward_batch(norm_x2)

        # Residual connection with scaling
        x = [add_vectors(orig, scale_vector(ff, self.residual_scale)) for orig, ff in zip(x, ff_out)]

        return x

class Encoder:
    def __init__(self, num_layers, d_model, d_ff, residual_scale=1.0, use_checkpointing=False):
        self.layers = [
            EncoderLayer(d_model, d_ff, residual_scale, use_checkpointing)
            for _ in range(num_layers)
        ]

    def forward(self, x):
        for layer in self.layers:
            x = layer.forward(x)
        return x

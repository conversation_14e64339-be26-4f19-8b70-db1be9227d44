import random
import math

# ----------- Basic Ops -----------

def dot_product(a, b):
    assert len(a) == len(b), "Dot product requires vectors of same length."
    return sum(x * y for x, y in zip(a, b))

def matmul(A, B):
    assert len(A[0]) == len(B), "Matrix dimensions do not align for multiplication."
    result = []
    for i in range(len(A)):
        row = []
        for j in range(len(B[0])):
            val = sum(A[i][k] * B[k][j] for k in range(len(B)))
            row.append(val)
        result.append(row)
    return result

def batch_matmul(batch_A, batch_B):
    return [matmul(A, B) for A, B in zip(batch_A, batch_B)]

def transpose(matrix):
    return [[row[i] for row in matrix] for i in range(len(matrix[0]))]

# ----------- Elementwise Ops -----------

def add_vectors(a, b):
    assert len(a) == len(b), "Vector addition requires same length."
    return [x + y for x, y in zip(a, b)]

def add_matrices(A, B):
    return [[a + b for a, b in zip(row_a, row_b)] for row_a, row_b in zip(A, B)]

def scalar_multiply_vector(vector, scalar):
    return [x * scalar for x in vector]

def scalar_multiply_matrix(matrix, scalar):
    return [[x * scalar for x in row] for row in matrix]

def scale_vector(vector, scalar):
    return scalar_multiply_vector(vector, scalar)

# ----------- Activations -----------

def relu(x):
    return [max(0, xi) for xi in x]

def backward_relu(grad_output, input_vector):
    return [g if x > 0 else 0 for g, x in zip(grad_output, input_vector)]

def gelu(x):
    return [xi * 0.5 * (1.0 + math.tanh(math.sqrt(2 / math.pi) * (xi + 0.044715 * xi**3))) for xi in x]

# ----------- Normalization -----------

def layer_norm(x, eps=1e-5):
    mean = sum(x) / len(x)
    var = sum((xi - mean) ** 2 for xi in x) / len(x)
    std = math.sqrt(var + eps)
    return [(xi - mean) / std for xi in x]

def batch_norm(batch, epsilon=1e-6):
    n = len(batch)
    d = len(batch[0])
    mean = [sum(vec[i] for vec in batch) / n for i in range(d)]
    var = [sum((vec[i] - mean[i])**2 for vec in batch) / n for i in range(d)]
    normed = []
    for vec in batch:
        normed.append([(vec[i] - mean[i]) / math.sqrt(var[i] + epsilon) for i in range(d)])
    return normed

# ----------- Softmax & Losses -----------

def softmax(vector):
    max_val = max(vector)
    exp_vals = [math.exp(x - max_val) for x in vector]
    sum_exp = sum(exp_vals)
    return [x / sum_exp for x in exp_vals]

def batch_softmax(batch):
    return [softmax(vec) for vec in batch]

def backward_softmax(softmax_output, target_index):
    grad = softmax_output[:]
    grad[target_index] -= 1
    return grad

def cross_entropy_loss(logits, target_class):
    probs = softmax(logits)
    return -math.log(probs[target_class] + 1e-8)

def mean_squared_error(pred, target):
    return sum((p - t) ** 2 for p, t in zip(pred, target)) / len(pred)

# ----------- Accuracy & Debug -----------

def accuracy(predictions, targets):
    correct = sum(1 for p, t in zip(predictions, targets) if p == t)
    return correct / len(targets)

def print_matrix(mat, name="Matrix"):
    print(f"\n{name}:")
    for row in mat:
        print(row)

# ----------- Initialization -----------

def initialize_matrix(rows, cols, value=0.01):
    return [[random.uniform(-0.1, 0.1) for _ in range(cols)] for _ in range(rows)]

# ----------- Attention Helpers -----------

def split_heads(x, num_heads):
    seq_len = len(x)
    d_model = len(x[0])
    assert d_model % num_heads == 0, "d_model must be divisible by num_heads"
    depth = d_model // num_heads
    heads = [[] for _ in range(num_heads)]
    for token in x:
        for i in range(num_heads):
            heads[i].append(token[i*depth:(i+1)*depth])
    return heads

def combine_heads(heads):
    seq_len = len(heads[0])
    return [[val for head in heads for val in head[i]] for i in range(seq_len)]

# ----------- Regularization -----------

def dropout(x, p, seed=None):
    if seed is not None:
        random.seed(seed)
    return [xi if random.random() > p else 0 for xi in x]

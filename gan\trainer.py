import random
from utils.math_ops import cross_entropy_loss


class Trainer:
    def __init__(self, generator, discriminator, tokenizer, vocab, max_len=10, temperature=1.0):
        self.generator = generator
        self.discriminator = discriminator
        self.tokenizer = tokenizer
        self.vocab = vocab
        self.max_len = max_len
        self.temperature = temperature

    def train(self, real_data, epochs=10):
        """
        real_data: List of tokenized and indexed real sentences
        """
        for epoch in range(epochs):
            print(f"\n--- Epoch {epoch + 1}/{epochs} ---")

            # Shuffle real data
            random.shuffle(real_data)

            for i, real_sample in enumerate(real_data):
                # === 1. Generate fake sentence ===
                fake_sample = self.generate_fake_sample()

                # === 2. Train Discriminator ===
                d_loss_real = self.train_discriminator(real_sample, label=1)
                d_loss_fake = self.train_discriminator(fake_sample, label=0)

                # === 3. Train Generator (indirectly via Discriminator's feedback) ===
                g_loss = self.train_generator(fake_sample)

                if i % 500 == 0:
                    print(f"[{i}/{len(real_data)}] D_loss_real={d_loss_real:.4f}, D_loss_fake={d_loss_fake:.4f}, G_loss={g_loss:.4f}")

    def generate_fake_sample(self):
        start_token = self.vocab["<START>"]
        generated = self.generator.generate([start_token], self.max_len)
        return generated

    def train_discriminator(self, sample, label):
        """
        sample: list of token ids
        label: 1 if real, 0 if fake
        """
        logits = self.discriminator.forward(sample)
        loss = cross_entropy_loss(logits[0], label)
        # Normally: backprop and update here
        return loss

    def train_generator(self, fake_sample):
        """
        Update generator based on how well it fools the discriminator.
        """
        # Let discriminator judge the fake
        logits = self.discriminator.forward(fake_sample)
        # Trick the discriminator: we want it to think fake is real (label=1)
        loss = cross_entropy_loss(logits[0], 1)
        # Normally: backprop to generator here (not implemented in this manual setup)
        return loss

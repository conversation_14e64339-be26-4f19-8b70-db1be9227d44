import random
from utils.math_ops import cross_entropy_loss


class Trainer:
    def __init__(self, generator, discriminator, vocab, rev_vocab=None, max_len=10, temperature=1.0, embedding_dim=128, hidden_dim=256, num_heads=4):
        self.generator = generator
        self.discriminator = discriminator
        self.vocab = vocab
        self.rev_vocab = rev_vocab
        self.max_len = max_len
        self.temperature = temperature
        self.embedding_dim = embedding_dim
        self.hidden_dim = hidden_dim
        self.num_heads = num_heads

    def train(self, real_data, epochs=10):
        """
        real_data: List of tokenized and indexed real sentences
        """
        for epoch in range(epochs):
            print(f"\n--- Epoch {epoch + 1}/{epochs} ---")
            epoch_d_losses = []
            epoch_g_losses = []

            # Shuffle real data
            random.shuffle(real_data)

            for i, real_sample in enumerate(real_data):
                # === 1. Generate fake sentence ===
                fake_sample = self.generate_fake_sample()

                # === 2. Train Discriminator ===
                d_loss_real = self.train_discriminator(real_sample, label=1)
                d_loss_fake = self.train_discriminator(fake_sample, label=0)

                # === 3. Train Generator (indirectly via Discriminator's feedback) ===
                g_loss = self.train_generator(fake_sample)

                # Track losses for epoch summary
                epoch_d_losses.append((d_loss_real + d_loss_fake) / 2)
                epoch_g_losses.append(g_loss)

                # Phase 4: Better progress tracking
                if i % 10 == 0:  # More frequent logging for quick demo
                    avg_d_loss = (d_loss_real + d_loss_fake) / 2
                    print(f"[{i}/{len(real_data)}] D_real={d_loss_real:.4f}, D_fake={d_loss_fake:.4f}, G={g_loss:.4f}, D_avg={avg_d_loss:.4f}")

                    # Show sample generation every 25 steps for quick demo
                    if i % 25 == 0 and i > 0:
                        sample = self.generate_fake_sample()
                        sample_text = " ".join([self.rev_vocab.get(tok, f"<{tok}>") for tok in sample[:5]])
                        print(f"    Sample: {sample_text}...")

            # Epoch summary
            avg_d_loss = sum(epoch_d_losses) / len(epoch_d_losses) if epoch_d_losses else 0
            avg_g_loss = sum(epoch_g_losses) / len(epoch_g_losses) if epoch_g_losses else 0
            print(f"Epoch {epoch + 1} Summary: D_avg={avg_d_loss:.4f}, G_avg={avg_g_loss:.4f}")

    def generate_fake_sample(self):
        start_token = self.vocab.get("<START>", 2)
        generated = self.generator.generate([start_token], self.max_len)
        return generated

    def train_discriminator(self, sample, label):
        """
        sample: list of token ids
        label: 1 if real, 0 if fake
        """
        logits = self.discriminator.forward(sample)
        # logits is a list of 2 values [fake_score, real_score]
        loss = cross_entropy_loss(logits, label)
        # Normally: backprop and update here
        return loss

    def train_generator(self, fake_sample):
        """
        Update generator based on how well it fools the discriminator.
        """
        # Let discriminator judge the fake
        logits = self.discriminator.forward(fake_sample)
        # Trick the discriminator: we want it to think fake is real (label=1)
        loss = cross_entropy_loss(logits, 1)
        # Normally: backprop to generator here (not implemented in this manual setup)
        return loss

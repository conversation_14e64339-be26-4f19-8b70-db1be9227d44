# transformer/feedforward.py

import math
import random
from utils.math_ops import add_vectors, dot_product, relu, gelu, layer_norm, dropout

class FeedForward:
    def __init__(self, d_model, d_ff, activation="gelu", use_layer_norm=True, dropout_prob=0.1):
        self.d_model = d_model
        self.d_ff = d_ff
        self.activation = activation.lower()
        self.use_layer_norm = use_layer_norm
        self.dropout_prob = dropout_prob

        # Xavier initialization for weights
        limit1 = math.sqrt(6 / (d_model + d_ff))
        self.W1 = [[random.uniform(-limit1, limit1) for _ in range(d_ff)] for _ in range(d_model)]
        self.b1 = [0.0 for _ in range(d_ff)]

        limit2 = math.sqrt(6 / (d_ff + d_model))
        self.W2 = [[random.uniform(-limit2, limit2) for _ in range(d_model)] for _ in range(d_ff)]
        self.b2 = [0.0 for _ in range(d_model)]

    def linear(self, x, W, b):
        # Simple linear transformation: xW + b
        result = []
        for j in range(len(W[0]) if W else 0):  # output dimension
            val = 0
            for i in range(len(x)):
                val += x[i] * W[i][j]
            result.append(val + b[j])
        return result

    def forward(self, x):
        if self.use_layer_norm:
            x = layer_norm(x)

        h = self.linear(x, self.W1, self.b1)

        # Activation function
        if self.activation == "relu":
            h = relu(h)
        elif self.activation == "gelu":
            h = gelu(h)
        else:
            raise ValueError(f"Unsupported activation: {self.activation}")

        h = dropout(h, self.dropout_prob)
        out = self.linear(h, self.W2, self.b2)

        return layer_norm(out) if self.use_layer_norm else out

    def forward_batch(self, inputs):
        # Apply feedforward block to each input vector
        return [self.forward(x) for x in inputs]

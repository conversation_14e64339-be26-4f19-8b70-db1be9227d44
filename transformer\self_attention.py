# transformer/self_attention.py
from utils.math_ops import dot_product, softmax, scale_vector, add_vectors, transpose
import math
import random

class SelfAttention:
    def __init__(self, d_model, num_heads=1, causal=False, dropout_rate=0.1):
        self.d_model = d_model
        self.num_heads = num_heads
        self.head_dim = d_model // num_heads
        self.causal = causal
        self.dropout_rate = dropout_rate

        # Xavier initialization
        def xavier_init(in_dim, out_dim):
            scale = math.sqrt(6 / (in_dim + out_dim))
            return [[random.uniform(-scale, scale) for _ in range(out_dim)] for _ in range(in_dim)]

        self.W_q = [xavier_init(d_model, self.head_dim) for _ in range(num_heads)]
        self.W_k = [xavier_init(d_model, self.head_dim) for _ in range(num_heads)]
        self.W_v = [xavier_init(d_model, self.head_dim) for _ in range(num_heads)]

        # Output projection (after concatenating heads)
        self.W_o = xavier_init(d_model, d_model)

    def linear(self, x, W):
        """Linear transformation: x (1D vector) x W (2D matrix)"""
        return [sum(x[i] * W[i][j] for i in range(len(x))) for j in range(len(W[0]))]

    def apply_mask(self, scores):
        """Apply causal mask: Prevent position i from attending to any j > i"""
        for i in range(len(scores)):
            for j in range(len(scores[i])):
                if j > i:
                    scores[i][j] = float('-inf')
        return scores

    def compute_attention(self, queries, keys, values):
        scores = [
            [dot_product(q, k) / math.sqrt(len(q)) for k in keys]
            for q in queries
        ]

        if self.causal:
            scores = self.apply_mask(scores)

        attention_weights = [softmax(row) for row in scores]

        # Dropout on attention weights
        if self.dropout_rate > 0:
            for i, row in enumerate(attention_weights):
                dropped = [
                    w if random.random() > self.dropout_rate else 0.0 for w in row
                ]
                total = sum(dropped)
                attention_weights[i] = [w / (total + 1e-8) for w in dropped] if total > 0 else dropped

        # Optional: save weights for visualization/debugging
        self.last_attention_weights = attention_weights

        output = []
        dim = len(values[0])
        for weights in attention_weights:
            context = [0.0] * dim
            for j, weight in enumerate(weights):
                context = add_vectors(context, scale_vector(values[j], weight))
            output.append(context)

        return output

    # Replace your current forward method in SelfAttention class with this

    def forward(self, inputs, context=None):
        """
        inputs: List of d_model-dimensional vectors (sequence length x d_model)
        context: Optional list of vectors for encoder-decoder attention
        returns: List of d_model-dimensional vectors
        """
        # Use inputs as context if none provided (self-attention)
        context = context if context is not None else inputs

        all_heads_output = []

        for h in range(self.num_heads):
            q = [self.linear(x, self.W_q[h]) for x in inputs]
            k = [self.linear(x, self.W_k[h]) for x in context]
            v = [self.linear(x, self.W_v[h]) for x in context]
            head_output = self.compute_attention(q, k, v)
            all_heads_output.append(head_output)

        # Concatenate heads
        concat = [
            sum([all_heads_output[h][i] for h in range(self.num_heads)], [])
            for i in range(len(inputs))
        ]

        # Final projection
        output = [self.linear(x, self.W_o) for x in concat]
        return output


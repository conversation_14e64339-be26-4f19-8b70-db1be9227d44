# nlp/embedding.py

import random
import json
from utils.math_ops import initialize_matrix

class Embedding:
    def __init__(self, vocab_size, embedding_dim, pad_token_id=0, sos_id=1, eos_id=2, unk_id=3, seed=42):
        self.vocab_size = vocab_size
        self.embedding_dim = embedding_dim
        self.pad_token_id = pad_token_id
        self.sos_id = sos_id
        self.eos_id = eos_id
        self.unk_id = unk_id

        random.seed(seed)  # for reproducibility
        self.embedding_matrix = initialize_matrix(vocab_size, embedding_dim)

        # Zero out pad vector
        self.embedding_matrix[pad_token_id] = [0.0] * embedding_dim

    def get_vector(self, word_id):
        if word_id < 0 or word_id >= self.vocab_size:
            return self.embedding_matrix[self.unk_id]  # fallback to <unk>
        return self.embedding_matrix[word_id]

    def embed_sentence(self, sentence_ids, dropout_rate=0.0):
        embedded = []
        for idx in sentence_ids:
            vec = self.get_vector(idx)
            if idx != self.pad_token_id and dropout_rate > 0:
                vec = [v if random.random() > dropout_rate else 0.0 for v in vec]
            embedded.append(vec)
        return embedded

    def embed_batch(self, batch_ids, dropout_rate=0.0):
        return [self.embed_sentence(sentence, dropout_rate) for sentence in batch_ids]

    def update(self, word_id, gradient_vector, learning_rate=0.01, clip_value=None):
        if word_id == self.pad_token_id:
            return  # don't update pad token
        for i in range(self.embedding_dim):
            grad = gradient_vector[i]
            if clip_value is not None:
                grad = max(min(grad, clip_value), -clip_value)
            self.embedding_matrix[word_id][i] -= learning_rate * grad

    def normalize(self):
        for i in range(self.vocab_size):
            norm = sum(v ** 2 for v in self.embedding_matrix[i]) ** 0.5 + 1e-8
            self.embedding_matrix[i] = [v / norm for v in self.embedding_matrix[i]]

    def save(self, path="embedding.json"):
        with open(path, "w", encoding="utf-8") as f:
            json.dump(self.embedding_matrix, f)

    def load(self, path="embedding.json"):
        with open(path, "r", encoding="utf-8") as f:
            self.embedding_matrix = json.load(f)
    def __call__(self, token_ids):
        if isinstance(token_ids, int):
            return self.get_vector(token_ids)
        elif isinstance(token_ids, list):
            return self.embed_sentence(token_ids)
        else:
            raise TypeError("Input to embedding must be int or list of ints.")
